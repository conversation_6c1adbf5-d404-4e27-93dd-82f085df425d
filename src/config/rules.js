/**
 * 集中存放所有需要便捷修改的数据
 * @module RULES
 */
export const RULES = {
  // 文本规则处理说明：
  // 以下规则在纯文本节点上运行，是注音转换的核心。
  //
  // 主要分为三类：
  // 1. MANUAL_MARK:
  //    - 用途: 强制标记单词边界，用于自动分词可能出错的特例。
  //    - 格式: '汉字（读音）' 格式的字符串。
  //    - 工作流程: 脚本会精确匹配并转换为 <ruby>。
  //
  // 2. FULL_REPLACE:
  //    - 用途: 完全手动控制最终的 HTML，用于最复杂的场景。
  //    - 格式: { pattern: '模式（任意内容）', replacement: '最终HTML字符串' }。
  //    - 工作流程: 直接将匹配 pattern 的内容替换为 replacement 字符串。

  // 文本层处理规则 (在纯文本节点上操作)
  TEXT: {
    // 手动标记：强制分割，作用是标记单词边界（普通词能自动匹配，此为例外）
    MANUAL_MARK: [
      '℃（ど）',
      'お団子（おだんご）',
      'お好み焼き（おこのみやき）',
      'お年寄り（おとしより）',
      'お店（おみせや）',
      'お茶する（おちゃする）',
      'お茶（おちゃ）',
      'ご先祖さま（ごせんぞさま）',
      'たつ年（たつどし）',
      'グリーン車（グリーンしゃ）',
      'コマ回し（こままわし）',
      'ダメ元（だめもと）',
      '一つ（ひとつ）',
      '万引き（まんびき）',
      '三分の一（さんぶんのいち）',
      '不確か（ふたしか）',
      '不足（ふそく）',
      '不足（ぶそく）',
      '世界１周旅行（せかいいっしゅうりょこう）',
      '並み（なみ）',
      '並（なら）',
      '中に（じゅうに）',
      '中旬（ちゅうじゅん）',
      '中（じゅう）',
      '予定通り（よていどおり）',
      '以上（いじょう）',
      '以下（いか）',
      '以外の何ものでもない（いがいのなにものでもない）',
      '以外（いがい）',
      '住（す）',
      '使い分け（つかいわけ）',
      '使い方（つかいかた）',
      '使用（しよう）',
      '働（はたら）',
      '優（すぐ）',
      '元を取る（もとをとる）',
      '元カノ（もとかの）',
      '元カレ（もとかれ）',
      '児（じ）',
      '入学（にゅうがく）',
      '入（い）',
      '入（はい）',
      '全て（すべて）',
      '全員（ぜんいん）',
      '公共の場（こうきょうのば）',
      '凧揚げ（たこあげ）',
      '出張（しゅっちょう）',
      '分（ぶん）',
      '前（まえ）',
      '割り勘（わりかん）',
      '割り箸（わりばし）',
      '動作（どうさ）',
      '収集（しゅうしゅう）',
      '取（と）',
      '受け身（うけみ）',
      '口の中（くちのなか）',
      '合（あ）',
      '同（おな）',
      '吐き気（はきけ）',
      '向（む）',
      '吸い物（すいもの）',
      '味覚 （みかく）',
      '呼び方（よびかた）',
      '唐揚げ（からあげ）',
      '商い（あきない）',
      '商品（しょうひん）',
      '土砂崩れ（どしゃくずれ）',
      '夏バテ防止（なつばてぼうし）',
      '夏休み中（なつやすみちゅう）',
      '夏向き（なつむき）',
      '夏祭り（なつまつり）',
      '夕ご飯（ゆうごはん）',
      '多かれ少なかれ（おおかれすくなかれ）',
      '大きかれ小さかれ（おおきかれちいさかれ）',
      '大切（たいせつ）',
      '大好き（だいすき）',
      '学習者（がくしゅうしゃ）',
      '宝くじ（たからくじ）',
      '寝る前（ねるまえ）',
      '寝（ね）',
      '届け出（とどけで）',
      '座り心地（すわりごこち）',
      '引っ越し（ひっこし）',
      '引っ越す（ひっこす）',
      '当たり前（あたりまえ）',
      '当（あ）',
      '役に立つ（やくにたつ）',
      '待（ま）',
      '後ろ（うしろ）',
      '必要（ひつよう）',
      '怒り（いかり）',
      '思い出す（おもいだす）',
      '思い出話（おもいでばなし）',
      '恵方巻き（えほうまき）',
      '悩み事（なやみごと）',
      '感じ方（かんじかた）',
      '我が社（わがしゃ）',
      '戦（せん）',
      '手作り（てづくり）',
      '払（はら）',
      '折があれば（おりがあれば）',
      '折に触れて（おりにふれて）',
      '折も折（おりもおり）',
      '折を見て（おりをみて）',
      '拭き取る（ふきとる）',
      '持ち家（もちいえ）',
      '掲載（けいさい）',
      '数え方（かぞえかた）',
      '文化（ぶんか）',
      '文法（ぶんぽう）',
      '旅行（りょこう）',
      '日記（にっき）',
      '早寝早起き（はやねはやおき）',
      '星の数ほどある（ほしのかずほどある）',
      '星の数ほどいる（ほしのかずほどいる）',
      '星の数（ほしのかず）',
      '昭和の日（しょうわのひ）',
      '暑（あつ）',
      '暮（ぐ）',
      '書き言葉（かきことば）',
      '有名（ゆうめい）',
      '梅雨入り（つゆいり）',
      '楽（たの）',
      '欠席（けっせき）',
      '歩（ある）',
      '残業（ざんぎょう）',
      '気を付けて（きをつけて）',
      '気持ち（きもち）',
      '物語（ものがたり）',
      '犬年（いぬどし）',
      '独り言（ひとりごと）',
      '瓜二つ（うりふたつ）',
      '甘い物（あまいもの）',
      '申し出（もうしで）',
      '申し訳（もうしわけ）',
      '男の子（おとこのこ）',
      '盗み食い（ぬすみぐい）',
      '目を離す（めをはなす）',
      '真っ暗（まっくら）',
      '真っ白（まっしろ）',
      '真っ茶色（まっちゃいろ）',
      '真っ黄色（まっきいろ）',
      '真っ黒（まっくろ）',
      '真ん中（まんなか）',
      '知り合い（しりあい）',
      '確か（たしか）',
      '社会（しゃかい）',
      '福笑い（ふくわらい）',
      '秋無い（あきない）',
      '程（ほど）',
      '空き缶（あきかん）',
      '窓の外（まどのそと）',
      '立ち読み（たちよみ）',
      '第２日曜日（だいににちようび）',
      '第２月曜日（だいにげつようび）',
      '笹の葉（ささのは）',
      '範囲（はんい）',
      '細長い（ほそながい）',
      '紹介（しょうかい）',
      '組み合わせ（くみあわせ）',
      '経（た）',
      '結婚（けっこん）',
      '繰り返して（くりかえして）',
      '繰（く）',
      '羽根つき（はねつき）',
      '考え方（かんがえかた）',
      '能力試験（のうりょくしけん）',
      '腹が立つ（はらがたつ）',
      '自身（じしん）',
      '良かれ悪しかれ（よかれあしかれ）',
      '芸術の秋（げいじゅつのあき）',
      '落ち着（おちつ）',
      '行き方（いきかた）',
      '行き渡る（いきわたる）',
      '観光地（かんこうち）',
      '触り心地（さわりごこち）',
      '試験（しけん）',
      '試（ため）',
      '話し手（はなして）',
      '話し言葉（はなしことば）',
      '詳（くわ）',
      '読み方（よみかた）',
      '読書の秋（どくしょのあき）',
      '請け合い（うけあい）',
      '豪雨（ごうう）',
      '貯金（ちょきん）',
      '貯（た）',
      '買い物（かいもの）',
      '貸し借り（かしかり）',
      '足が早い（あしがはやい）',
      '車内（しゃない）',
      '載（の）',
      '返（かえ）',
      '逃（に）',
      '通り（とおり）',
      '通り（どおり）',
      '通知（つうち）',
      '通（どお）',
      '連続（れんぞく）',
      '遅かれ早かれ（おそかれはやかれ）',
      '遅刻（ちこく）',
      '長い間（ながいあいだ）',
      '長生き（ながいき）',
      '長（なが）',
      '間違え（まちがえ）',
      '間（かん）',
      '雨の日（あめのひ）',
      '雪遊び（ゆきあそび）',
      '震える（ふるえる）',
      '青い色（あおいいろ）',
      '青のり（あおのり）',
      '青リンゴ（あおりんご）',
      '頭の中（あたまのなか）',
      '願い事（ねがいごと）',
      '食べず嫌い（たべずぎらい）',
      '食べ物（たべもの）',
      '食欲の秋（しょくよくのあき）',
      '食（しょく）',
      '飲み会（のみかい）',
      '飲み口（のみぐち）',
      '飲み放題（のみほうだい）',
      '飲み物（のみもの）',
      '飼い主（かいぬし）',
      '飽きない（あきない）',
      '駅（えき）',
      '驚き（おどろき）',
      '髪の毛（かみのけ）',
      '鳴き声（なきごえ）',
      '０点（れいてん）',
      '１か月間（いっかげつかん）',
      '１か月（いっかげつ）',
      '１つ（ひとつ）',
      '１人（ひとり）',
      '１列（いちれつ）',
      '１口（ひとくち）',
      '１回（いっかい）',
      '１年中（いちねんじゅう）',
      '１年（いちねん）',
      '１度（いちど）',
      '１日中（いちにちじゅう）',
      '１日（いちにち）',
      '１日（ついたち）',
      '１本（いっぽん）',
      '１杯（いっぱい）',
      '１歩（いっぽ）',
      '１泊（いっぱく）',
      '１番目（いちばんめ）',
      '１番（いちばん）',
      '１週間後（いっしゅうかんご）',
      '１０日間（とおかかん）',
      '１０日（とおか）',
      '１０杯（じゅっぱい）',
      '２人（ふたり）',
      '２日（ふつか）',
      '２本（にほん）',
      '２０歳（はたち）',
      '３日間（みっかかん）',
      '３日（みっか）',
      '３杯（さんばい）',
      '５分（ごふん）',
      '５日分（いつかぶん）',
      '５日前（いつかまえ）',
      '５日間（いつかかん）',
      '５月（ごがつ）',
      '７日（なのか）',
      '７時（しちじ）',
      '９月（くがつ）',
      '乗り心地（のりごこち）',
    ],

    // 全替换：提供模式和最终的 HTML，用于最复杂的送假名等情况
    FULL_REPLACE: [
      { pattern: '羽根を伸ばす（羽根を伸ばす）', replacement: '羽根を伸ばす（はねをのばす）' },
      { pattern: '長蛇の列（長蛇の列）', replacement: '長蛇の列（ちょうだのれつ）' },
      { pattern: '食べ物（食べ物）', replacement: '食べ物（たべもの）' },
      { pattern: '今回（今回）', replacement: '今回（こんかい）' },
      { pattern: '店長（店長）', replacement: '店長（てんちょう）' },
      { pattern: '一般的（いっぱん）', replacement: '一般的（いっぱんてき）' },
      { pattern: '付き合（つきあい）', replacement: '付き合（つきあ）' },
      { pattern: '汚（きたない）', replacement: '汚（きたな）' },
      { pattern: '必ず（かなら）', replacement: '必ず（かならず）' },
      { pattern: '恥（はず）', replacement: '恥（は）' },
      { pattern: '足（たり）', replacement: '足（た）' },
      { pattern: '楽（他の）', replacement: '楽（たの）' },
      { pattern: '読（よん）', replacement: '読（よ）' },
      { pattern: '調（しらべた）', replacement: '調（しら）' },
      { pattern: '間違（街が）', replacement: '間違（まちが）' },
      { pattern: '間違（まちがえ）', replacement: '間違（まちが）' },
      { pattern: '難（むず）', replacement: '難（むずか）' },
      { pattern: '寂（さみ）', replacement: '寂（さび）' },
      { pattern: '彼氏（かれ）', replacement: '彼氏（かれし）' },
      { pattern: '父（とうさん）', replacement: '父（とう）' },
      { pattern: '特別（とく） ', replacement: '特別（とくべつ）' },
      { pattern: '逆ギレ（ぎゃくぎれ）', replacement: '逆（ぎゃく）ギレ' },
      { pattern: '娘（ムスメ）', replacement: '娘（むすめ）' },
      { pattern: '車（クルマ）', replacement: '車（くるま）' },
      { pattern: 'm（めーとる）', replacement: 'm（メートル）' },
      { pattern: 'L（L）', replacement: 'L（エル）' },
      { pattern: 'XL（エックスL）', replacement: 'XL（エックスエル）' },
      { pattern: 'Yシャツ（ワイシャツ）', replacement: 'Y（ワイ）シャツ' },
      { pattern: 'Yシャツ（わいしゃつ）', replacement: 'Y（ワイ）シャツ' },
      { pattern: 'のる（乗る）', replacement: '乗る（のる）' },
      { pattern: 'ところ（所）', replacement: '所（ところ）' },
      { pattern: 'いいふうふ（いい夫婦）', replacement: 'いい夫婦（いいふうふ）' },
      { pattern: '耳が痛い（みみがいたい）', replacement: '耳（みみ）が痛（いた）い' },
      { pattern: 'マイ〇〇（my+〇〇）', replacement: 'マイ（my）〇〇' },
      { pattern: '目に余る②（めにあまる）', replacement: '目に余る（めにあまる）②' },
      { pattern: '何のこと？（なんのこと）', replacement: '何のこと（なんのこと）？' },
      { pattern: '冷める（さめる・自動詞）', replacement: '冷める（さめる）（自動詞）' },
      { pattern: '冷ます（さます・他動詞）', replacement: '冷ます（さます）（他動詞）' },
      { pattern: '聞き手（ききて）', replacement: '聞（き）き手（て）' },
      { pattern: '言い方（いいかた）', replacement: '言（い）い方（かた）' },
      { pattern: '言い訳（いいわけ）', replacement: '言（い）い訳（わけ）' },
      { pattern: '年越しそば（としこしそば）', replacement: '年越（としこ）しそば' },
      { pattern: '顔から火が出る（かおからひがでる）', replacement: '顔（かお）から火（ひ）が出（で）る' },
      { pattern: '頭が痛い（あたまがいたい）', replacement: '頭（あたま）が痛（いた）い' },
      { pattern: '歯が立たない（はがたたない）', replacement: '歯（は）が立（た）たない' },
      { pattern: '色違い（いろちがい）', replacement: '色（いろ）違（ちが）い' },
      { pattern: '原因・理由（げんいん・りゆう）', replacement: '原因（げんいん）・理由（りゆう）' },
      { pattern: '目の色が変わる・目の色を変える（めのいろがかわる・かえる）', replacement: '目の色が変わる（めのいろがかわる）・目の色を変える（めのいろをかえる）' },
      { pattern: '青菜・青野菜（あおな・あおやさい）', replacement: '青菜（あおな）・青野菜（あおやさい）' },
      { pattern: '水の泡になる・水の泡となる（みずのあわになる）', replacement: '水の泡になる（みずのあわになる）・水の泡となる（みずのあわとなる）' },
      { pattern: '意味で（いみ）', replacement: '意味（いみ）で' },
      { pattern: '和製英語で（わせいえいご）', replacement: '和製英語（わせいえいご）で' },
      { pattern: '財布を（さいふ）', replacement: '財布（さいふ）を' },
      { pattern: 'ソーシャル・ネットワーキング・サービス（Social Networking Service）', replacement: 'ソーシャル（Social）・ネットワーキング（Networking）・サービス（Service）' },
      { pattern: 'サボ（さぼ）って', replacement: '<em><ruby>サボ<rt>さぼ</rt></ruby>って</em>' },
      { pattern: '何（なに・なん）', replacement: '<ruby>何<rt>なに・なん</rt></ruby>' },
      { pattern: '何って人（なんって人）', replacement: '何（なん）って人' },
      { pattern: '何て人 （なんて人）', replacement: '何（なん）て人' },
      { pattern: 'お言葉に甘えて〜する（おことばにあまえて〜する）', replacement: 'お言葉（ことば）に甘（あま）えて〜する' },
    ],
  },

  // HTML 规则处理说明：
  // 以下规则在文本处理引擎之前运行，直接对元素的 innerHTML 进行操作。
  // 这允许我们预处理复杂的、跨越多个 HTML 标签的模式，将其简化为后续文本引擎能够理解的格式。
  //
  // 主要分为三类：
  // 1. MANUAL_MARK:
  //    - 用途: 简化那些因包含 HTML 标签而难以直接在文本层处理的简单注音。
  //    - 格式: 一个包含 HTML 的字符串，例如 '甘み</b>（あまみ）'。
  //    - 工作流程: 脚本会自动移除所有 HTML 标签，将结果（例如 '甘み（あまみ）'）交给后续的文本引擎处理。
  //
  // 2. FULL_REPLACE:
  //    - 用途: 提供最大的灵活性，用于完全手动的、端到端的替换。
  //    - 格式: { pattern: '字符串或RegExp', replacement: '任意字符串' }。
  //    - 工作流程: 直接将匹配 pattern 的内容替换为 replacement 字符串。这对于实现"两阶段处理"至关重要：
  //      - 示例：可以将一个复杂的 HTML 结构先替换为一个简化的、包含括号注音的纯文本格式（如 '（しごと）<em>上（じょう）</em>'）。
  //      - 这样，后续的文本处理引擎就能识别并正确地将其转换为最终的 <ruby> 注音。

  // HTML 层处理规则 (在 innerHTML 上操作，用于处理跨节点等复杂场景)
  HTML: {
    // 手动标记：自动移除 HTML 标签，简化为纯文本模式处理
    MANUAL_MARK: [
      // 支持 '?' 在任意位置，处理非标准读音
      'エアーコンディショナー（?air conditioner）',
      'マフラー（muffler?）',
      '真っ赤（まっ?か）',
      '真っ青（まっ?さお）',
      '子（こ?）',
      // 使用 '?' 作为任意 HTML 标签的占位符
      '今?（いま）',
      '歌手?（かしゅ）',
      '後?（うし）',
      '的?（てき）',
      '甘み?（あまみ）',
      '辛み?（からみ）',
      '苦み?（にがみ）',
      '苦しみ?（くるしみ）',
      '深み?（ふかみ）',
      '面白み?（おもしろみ）',
      '強み?（つよみ）',
      '弱み?（よわみ）',
      '痛み?（いたみ）',
      '赤み?（あかみ）',
      '青み?（あおみ）',
      '厚み?（あつみ）',
      '憐れみ?（あわれみ）',
      '生きがい?（いきがい）',
      '教えがい?（おしえがい）',
      '育てがい?（そだてがい）',
      '作りがい?（つくりがい）',
      '状態?（じょうたい）',
      '軒並み?（のきなみ）',
      '家並み?（いえなみ）',
      '並外れた?（なみはずれた）',
      '望む?（のぞむ）',
      '期待する?（きたいする）',
      '希望する?（きぼうする）',
      '場合?（ばあい）',
      '居?（い）',
      '留守?（るす）',
      '失望?（しつぼう）',
      '非難?（ひなん）',
      '不満?（ふまん）',
      '軽蔑?（けいべつ）',
      '運動?（うんどう）',
      '戸?（と）',
      '扉?（とびら）',
      '次第?（しだい）',
      '当然?（とうぜん）',
      '安全上?（あんぜんじょう）',
      '全然?（ぜんぜん）',
      '仕事上?（しごとじょう）',
      '教育上?（きょういくじょう）',
      '歴史上?（れきしじょう）',
      '健康上?（けんこうじょう）',
      '立場上?（たちばじょう）',
      '法律上?（ほうりつじょう）',
      '経験上?（けいけんじょう）',
      '数年?（すうねん）',
      '週間?（しゅうかん）',
      '数日間?（すうじつかん）',
      '絶対?（ぜったい）',
      '何かにつけ?（なにかにつけ）',
      '泣くに泣けない?（なくになけない）',
      '言うに言えない?（いうにいえない）',
      '引くに引けない?（ひくにひけない）',
      '時?（とき）',
      '勉強中?（べんきょうちゅう）',
      '電話中?（でんわちゅう）',
      '親離れ?（おやばなれ）',
      '子離れ?（こばなれ）',
      '乳離れ?（ちばなれ）',
      '巣離れ?（すばなれ）',
      '直前?（ちょくぜん）',
      '最中?（さいちゅう）',
      '直後?（ちょくご）',
      '言葉?（ことば）',
    ],

    // 全替换：将复杂的 HTML 模式直接替换为指定内容，用于两阶段处理
    FULL_REPLACE: [
      // 复杂状况，手动编写 ruby
      { pattern: '復習（ふくしゅう<br>）', replacement: '復習（ふくしゅう）' },
      { pattern: '一瞬（いっしゅん<br>）', replacement: '一瞬（いっしゅん）' },
      { pattern: '<b style="background-color: rgb(255, 255, 0);">２</b>日（ふつか）', replacement: '<b style="background-color: rgb(255, 255, 0);">２（ふつ）</b>日（か）' },
      { pattern: '<b>１</b>日（いちにち）', replacement: '<b style="background-color: rgb(255, 255, 0);">１（いち）</b>日（にち）' },
      { pattern: '<b>３日</b></span><b>（</b>みっか）', replacement: '<b>３日（みっか）</b></span>' },
      { pattern: '既読スルー</b></span>（きどくするー）', replacement: '既読スルー（きどくスルー）</b></span>' },
      { pattern: '何事につけ</b></span>（なにごとにつけ）', replacement: '何事（なにごと）につけ</b></span>' },
      { pattern: '<span style="color: rgb(255, 0, 0);"><b>上</b></span>（しごとじょう）', replacement: '（しごと）<em>上（じょう）</em>' },
      { pattern: '<b><span style="color: rgb(255, 0, 0);">上</span></b>（きょういくじょう）', replacement: '（きょういく）<em>上（じょう）</em>' },
      { pattern: '<span style="color: rgb(255, 0, 0);"><b>上</b></span>（れきしじょう）', replacement: '（れきし）<em>上（じょう）</em>' },
      { pattern: '<b><span style="color: rgb(255, 0, 0);">上</span></b>（けんこうじょう）', replacement: '（けんこう）<em>上（じょう）</em>' },
      { pattern: '<span style="color: rgb(255, 0, 0);"><b>上</b></span>（たちばじょう）', replacement: '（たちば）<em>上（じょう）</em>' },
      { pattern: '<span style="color: rgb(255, 0, 0);"><b>上</b></span>（ほうりつじょう）', replacement: '（ほうりつ）<em>上（じょう）</em>' },
      { pattern: '<span style="color: rgb(255, 0, 0);"><b>上</b></span>（けいけんじょう）', replacement: '（けいけん）<em>上（じょう）</em>' },
      { pattern: '<span style="color: rgb(255, 0, 0);"><b>上</b></span>（あんぜんじょう）', replacement: '（あんぜん）<em>上（じょう）</em>' },
      { pattern: '<span style="color: rgb(255, 0, 0);"><b>中</b></span><b><span style="color: rgb(255, 0, 0);">（きょうじゅう）に</span></b>', replacement: '（きょう）<em>中（じゅう）に</em>' },
      { pattern: '<b><span style="color: rgb(255, 0, 0);">中（あすじゅう）に</span></b>', replacement: '（あす）<em>中（じゅう）に</em>' },
      { pattern: '<b><span style="color: rgb(255, 0, 0);">中（ことしじゅう）に</span></b>', replacement: '（ことし）<em>中（じゅう）に</em>' },
      { pattern: '<b><span style="color: rgb(255, 0, 0);">中（かいしゃじゅう）に</span></b>', replacement: '（かいしゃ）<em>中（じゅう）に</em>' },
      { pattern: '真っ赤（まっ<span style="background-color: rgb(204, 204, 204);"><span style="color: rgb(255, 0, 0);">か</span></span>）', replacement: '真（ま）っ<em style="background-color: rgb(204, 204, 204);">赤（か）</em>' },
      { pattern: '真っ青（まっ<span style="background-color: rgb(204, 204, 204);"><span style="color: rgb(255, 0, 0);">さお</span></span>）', replacement: '真（ま）っ<em style="background-color: rgb(204, 204, 204);">青（さお）</em>' },
      { pattern: '<span style="font-size: 125%;">パト</span>ロール<span style="font-size: 125%;">カー</span>(patrol car)', replacement: '<span style="font-size: 125%;">パト(patrol)</span>ロール<span style="font-size: 125%;">カー(car)</span>' },
      { pattern: '白い（<span style="background-color: rgb(204, 204, 204);">しろ</span>い）', replacement: '<span style="background-color: rgb(204, 204, 204);">白（しろ）</span>い' },
      { pattern: '素人（<span style="background-color: rgb(204, 204, 204);">しろ</span>うと）', replacement: '<span style="background-color: rgb(204, 204, 204);">素（しろ）</span>うと' },
      { pattern: '毎</b></span>日（まいにち）', replacement: '毎（まい）</b></span>日（にち）' },
      { pattern: '毎</b></span>週（まいしゅう）', replacement: '毎（まい）</b></span>週（しゅう）' },
      { pattern: '毎</b></span>月（まいつき）', replacement: '毎（まい）</b></span>月（つき）' },
      { pattern: '毎</span></b>年（まいとし）', replacement: '毎（まい）</span></b>年（とし）' },
      { pattern: '何皿<b><span style="color: rgb(255, 0, 0);">目</span></b>（なんさらめ）', replacement: '何皿（なんさら）<em>目（め）</em>' },
      { pattern: '３つ<b><span style="color: rgb(255, 0, 0);">目</span></b>（みっつめ）', replacement: '３つ（みっつ）<em>目（め）</em>' },
      { pattern: '１０日<b><span style="color: rgb(255, 0, 0);">目</span></b>（とおかめ）', replacement: '１０日（とおか）<em>目（め）</em>' },
    ],
  },

  // 排除规则 用于防止脚本对特定内容进行错误的注音转换
  EXCLUDE: {
    // 全局排除：匹配完整的字符串，例如 '人称（私）'
    STRINGS: new Set([
      // '道草（を）',
      '元気（な）',
      '円（だ）',
      '挙句（に）',
      '矢先（に）',
      '際（に）',
      '末（に）',
      '以上（は）',
      '人称（あなた）',
      '二人称（あなた）',
      '女性（おばあちゃん）',
    ]),

    // 助词排除：当括号前是汉字，读音是以下助词时，不进行注音转换
    PARTICLES: new Set(['を']),
  },
}
