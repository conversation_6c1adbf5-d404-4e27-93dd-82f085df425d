# RubyConverter 单元测试

## 概述

这个目录包含了 RubyConverter 模块的单元测试，专注于测试核心工具方法的功能和边界情况。

## 文件结构

- `RubyConverter.test.js` - 主要的测试文件，包含所有测试用例
- `run-tests.js` - 测试运行器脚本
- `README.md` - 本文档

## 测试覆盖的方法

### 1. `_katakanaToHiragana(str)`
- ✅ 基本片假名转平假名转换
- ✅ 混合文本处理
- ✅ 空字符串和 null/undefined 处理
- ✅ 长音符号转换

### 2. `_escapeRegExp(string)`
- ✅ 基本特殊字符转义
- ✅ 普通文本不变
- ✅ 空字符串处理
- ✅ 混合内容转义

### 3. `_extractRubyCandidates(replacement)`
- ✅ 单个词条提取
- ✅ 多个词条提取
- ✅ 无效词条过滤
- ✅ 空文本处理
- ✅ 无匹配内容处理

### 4. 正则表达式缓存机制
- ✅ 缓存行为验证（基础测试）

## 运行测试

### 方法 1: 直接运行测试文件
```bash
node test/RubyConverter.test.js
```

### 方法 2: 使用测试运行器
```bash
node test/run-tests.js
```

### 方法 3: 添加到 package.json（推荐）
在项目根目录的 `package.json` 中添加：

```json
{
  "scripts": {
    "test": "node test/run-tests.js",
    "test:ruby": "node test/RubyConverter.test.js"
  }
}
```

然后运行：
```bash
npm test
# 或
npm run test:ruby
```

## 测试输出示例

```
🧪 开始运行 RubyConverter 单元测试...

✅ 片假名转平假名 - 基本转换
✅ 片假名转平假名 - 混合文本
✅ 片假名转平假名 - 空字符串
✅ 片假名转平假名 - null/undefined
✅ 片假名转平假名 - 长音符号
✅ 正则转义 - 基本特殊字符
✅ 正则转义 - 普通文本
✅ 正则转义 - 空字符串
✅ 正则转义 - 混合内容
✅ 词条提取 - 单个词条
✅ 词条提取 - 多个词条
✅ 词条提取 - 无效词条过滤
✅ 词条提取 - 空文本
✅ 词条提取 - 无匹配内容
✅ 正则表达式缓存 - 重复模式缓存

📊 测试结果: 15/15 通过
✅ 所有测试通过!
```

## 性能测试

这些单元测试也可以用于性能基准测试。优化后的方法应该：

1. **减少正则表达式编译次数** - 通过缓存机制
2. **提高字符串处理效率** - 避免不必要的操作
3. **减少内存分配** - 重用对象和数组

## 扩展测试

如需添加更多测试用例，请在 `RubyConverter.test.js` 中添加新的测试方法，并在 `runAllTests()` 中调用。

## 注意事项

- 这些是单元测试，专注于测试单个方法的功能
- 集成测试需要在实际的 DOM 环境中进行
- 性能测试需要大量数据和多次运行来获得准确结果
